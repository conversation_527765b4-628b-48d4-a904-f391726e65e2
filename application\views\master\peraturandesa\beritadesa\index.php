<?php defined('BASEPATH') or die('No direct script access allowed!'); ?>
<div class="page-header">
    <div class="page-block">
        <div class="row align-items-center">
            <div class="col-md-12">
                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= base_url(); ?>">Home</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url('master/peraturandesa'); ?>">Peraturan Desa</a></li>
                    <li class="breadcrumb-item" aria-current="page">Berita Desa</li>
                </ul>
            </div>
            <div class="col-md-12">
                <div class="page-header-title">
                    <h2 class="mb-0">Berita Desa</h2>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <h4>Data Berita Desa</h4>
                </div>
                <div></div>
            </div>
            <div class="card-body">
                <form action="<?= base_url(uri_string()) ?>" method="GET" autocomplete="off">
                    <div class="row align-items-end">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">Tanggal Mulai (Berita)</label>
                                <input type="date" name="startdate" class="form-control" value="<?= $startdate ?>">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">Tanggal Selesai (Berita)</label>
                                <input type="date" name="enddate" class="form-control" value="<?= $enddate ?>">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <button type="submit" class="btn btn-primary">Filter</button>
                                <a href="<?= base_url(uri_string()) ?>" class="btn btn-warning">Reset</a>
                                <button type="button" class="btn btn-danger" onclick="exportData()">Export PDF</button>
                            </div>
                        </div>
                    </div>
                </form>

                <div class="table-responsive">
                    <table class="table table-striped datatables">
                        <thead>
                            <tr>
                                <th>No. Urut</th>
                                <th>Jenis</th>
                                <th>Nomor Ditetapkan</th>
                                <th>Tanggal Ditetapkan</th>
                                <th>Tentang</th>
                                <th>Uraian Singkat</th>
                                <th>Nomor Berita Desa</th>
                                <th>Tanggal Berita Desa</th>
                                <th>Status</th>
                                <th>Dibuat Oleh</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($beritadesa as $key => $value): ?>
                                <tr>
                                    <td><?= $value->nomor_urut ?? '-' ?></td>
                                    <td><span class="badge badge-info"><?= $value->jenis_peraturan ?></span></td>
                                    <td><?= $value->nomor_ditetapkan ?? $value->nomor ?? '-' ?></td>
                                    <td><?= $value->tanggal_ditetapkan ? tgl_indo(date('Y-m-d', strtotime($value->tanggal_ditetapkan))) : ($value->date ? tgl_indo(date('Y-m-d', strtotime($value->date))) : '-') ?></td>
                                    <td><?= $value->tentang ?? '-' ?></td>
                                    <td><?= substr($value->uraiansingkat, 0, 50) . (strlen($value->uraiansingkat) > 50 ? '...' : '') ?></td>
                                    <td><?= $value->nomor_berita_desa ?? '-' ?></td>
                                    <td><?= $value->tanggal_berita_desa ? tgl_indo(date('Y-m-d', strtotime($value->tanggal_berita_desa))) : '-' ?></td>
                                    <td>
                                        <?php $status = $value->status_keterangan ?? 'Berlaku';
                                        $badge = $status=='Berlaku'?'badge-success':($status=='Diubah'?'badge-warning':($status=='Dicabut'?'badge-danger':'badge-secondary')); ?>
                                        <span class="badge <?= $badge ?>"><?= $status ?></span>
                                    </td>
                                    <td><?= $value->createdname ?> - <?= $value->createdusername ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function exportData() {
    let startdate = $('input[name="startdate"]').val();
    let enddate = $('input[name="enddate"]').val();
    window.open(`<?= base_url('master/peraturandesa/export_beritadesa') ?>?startdate=${startdate}&enddate=${enddate}`, '_blank');
}
</script>


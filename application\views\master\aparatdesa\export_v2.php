<!DOCTYPE html>
<html lang="id">

<head>
  <meta charset="UTF-8" />
  <title>Export Buku Aparat Pemerintah Desa</title>
  <style>
    * {
      box-sizing: border-box;
    }

    body {
      font-family: Arial, sans-serif;
      font-size: 10px;
    }

    .pdf-container {
      width: 258mm;
      margin: 0 auto;
    }

    .kop {
      text-align: center;
      margin-bottom: 10px;
    }

    .kop .title {
      font-weight: bold;
    }

    table.table {
      width: 100%;
      border-collapse: collapse;
      table-layout: fixed;
    }

    table.table th,
    table.table td {
      border: 0.5px solid #000;
      padding: 3px;
      font-size: 8px;
      line-height: 1.2;
      vertical-align: top;
      word-wrap: break-word;
      overflow-wrap: anywhere;
    }

    table.table th {
      text-align: center;
      font-weight: normal;
    }

    /* Tabel tanda tangan: 3 kolom proporsional yang pas area cetak */
    .sig {
      width: 100%;
      border-collapse: collapse;
      table-layout: fixed;
    }

    .sig td {
      border: 0;
      padding: 4px 0;
      text-align: center;
    }

    .sig .spacer {
      width: 18.6mm;
    }

    /* ~10% dari 186mm */
    .sig .wide {
      width: calc((100% - 18.6mm)/2);
    }

    .uline {
      display: inline-block;
      width: 70mm;
      border-bottom: 0.6px solid #000;
      margin-top: 20mm;
    }

    hr {
      border: 0;
      border-top: 1px solid #000;
      margin: 8px 0 16px;
    }
  </style>
</head>

<body>
  <div class="pdf-container">
    <!-- HEADER -->
    <table class="center" style="text-align:center; margin-bottom: 20px;">
      <tr>
        <th rowspan="4" style="width: 30mm; text-align:right; padding-right:10mm;">
          <img src="<?= base_url('/assets/img/logo-bpd.png') ?>" alt="logo" style="width: 26mm; height: 26mm;">
        </th>
        <th><b>BADAN PERMUSYAWARATAN DESA</b></th>
      </tr>
      <tr>
        <th><b>(BPD)</b></th>
      </tr>
      <tr>
        <th><b>DESA <?= $user->desaname ?> KECAMATAN <?= $user->kecamatanname ?></b></th>
      </tr>
      <tr>
        <th><b>KABUPATEN <?= $user->kabkotaname ?></b></th>
      </tr>
    </table>

    <hr>

    <!-- TABEL UTAMA -->
    <table class="table">
      <colgroup>
        <col style="width: 8mm"> <!-- 1. Nomor Urut -->
        <col style="width: 25mm"> <!-- 2. Nama -->
        <col style="width: 15mm"> <!-- 3. NIAP -->
        <col style="width: 15mm"> <!-- 4. NIP -->
        <col style="width: 10mm"> <!-- 5. JK -->
        <col style="width: 20mm"> <!-- 6. Tempat & Tgl Lahir -->
        <col style="width: 15mm"> <!-- 7. Agama -->
        <col style="width: 19mm"> <!-- 8. Pangkat Golongan -->
        <col style="width: 23mm"> <!-- 9. Jabatan -->
        <col style="width: 21mm"> <!-- 10. Pendidikan Terakhir -->
        <col style="width: 26mm"> <!-- 11. No & Tgl KP Pengangkatan -->
        <col style="width: 26mm"> <!-- 12. No & Tgl KP Pemberhentian -->
        <col style="width: 20mm"> <!-- 13. Ket -->
      </colgroup>
      <thead>
        <tr>
          <th>NOMOR URUT</th>
          <th>NAMA</th>
          <th>NIAP</th>
          <th>NIP</th>
          <th>JENIS KELAMIN</th>
          <th>TEMPAT DAN TGL LAHIR</th>
          <th>AGAMA</th>
          <th>PANGKAT GOLONGAN</th>
          <th>JABATAN</th>
          <th>PENDIDIKAN TERAKHIR</th>
          <th>NOMOR DAN TANGGAL KEPUTUSAN PENGANGKATAN</th>
          <th>NOMOR DAN TANGGAL KEPUTUSAN PEMBERHENTIAN</th>
          <th>KET</th>
        </tr>
        <tr>
          <th>1</th>
          <th>2</th>
          <th>3</th>
          <th>4</th>
          <th>5</th>
          <th>6</th>
          <th>7</th>
          <th>8</th>
          <th>9</th>
          <th>10</th>
          <th>11</th>
          <th>12</th>
          <th>13</th>
        </tr>
      </thead>
      <tbody>
        <?php foreach ($aparat as $key => $value): ?>
          <tr>
            <td><?= $value->nomor_urut ?? ($key + 1) ?></td>
            <td><?= wordwrap($value->nama ?? '-', 14, '<br>', true) ?></td>
            <td><?= $value->niap ?? '-' ?></td>
            <td><?= $value->nip ?? '-' ?></td>
            <td><?= $value->jenis_kelamin == 'Laki-laki' ? 'L' : ($value->jenis_kelamin == 'Perempuan' ? 'P' : '-') ?></td>
            <td>
              <?php
              $ttl = '';
              if (!empty($value->tempat_lahir)) $ttl .= $value->tempat_lahir;
              if (!empty($value->tanggal_lahir)) {
                if ($ttl) $ttl .= ', ';
                $ttl .= date('d/m/Y', strtotime($value->tanggal_lahir));
              }
              echo $ttl ?: '-';
              ?>
            </td>
            <td><?= $value->agama ?? '-' ?></td>
            <td><?= wordwrap($value->pangkat_golongan ?? '-', 10, '<br>', true) ?></td>
            <td><?= wordwrap($value->jabatan ?? '-', 12, '<br>', true) ?></td>
            <td><?= wordwrap($value->pendidikan_terakhir ?? '-', 12, '<br>', true) ?></td>
            <td>
              <?php
              $sk_angkat = '';
              if (!empty($value->no_sk_pengangkatan)) $sk_angkat .= $value->no_sk_pengangkatan;
              if (!empty($value->tanggal_sk_pengangkatan)) {
                if ($sk_angkat) $sk_angkat .= '<br>';
                $sk_angkat .= date('d/m/Y', strtotime($value->tanggal_sk_pengangkatan));
              }
              echo $sk_angkat ?: '-';
              ?>
            </td>
            <td>
              <?php
              $sk_berhenti = '';
              if (!empty($value->no_sk_pemberhentian)) $sk_berhenti .= $value->no_sk_pemberhentian;
              if (!empty($value->tanggal_sk_pemberhentian)) {
                if ($sk_berhenti) $sk_berhenti .= '<br>';
                $sk_berhenti .= date('d/m/Y', strtotime($value->tanggal_sk_pemberhentian));
              }
              echo $sk_berhenti ?: '-';
              ?>
            </td>
            <td><?= wordwrap($value->keterangan ?? '-', 14, '<br>', true) ?></td>
          </tr>
        <?php endforeach; ?>
      </tbody>
    </table>

    <!-- TANDA TANGAN -->
    <div style="margin-top: 28px;">
      <table class="sig">
        <tr>
          <td class="wide">Mengetahui,</td>
          <td class="spacer"></td>
          <td class="wide"><?= $user->desaname ?>, <?= tgl_indo(getCurrentDate('Y-m-d')) ?></td>
        </tr>
        <tr>
          <td class="wide">Ketua BPD</td>
          <td class="spacer"></td>
          <td class="wide">Sekertaris BPD</td>
        </tr>
      </table>

      <div style="height: 40mm;"></div>

      <table class="sig">
        <tr>
          <td class="wide">(_______________)</td>
          <td class="spacer"></td>
          <td class="wide">(_______________)</td>
        </tr>
      </table>
    </div>
  </div>
</body>

</html>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Export Keputusan Kepala Desa</title>

    <style>
        /* Area cetak & margin kertas */
        @page {
            size: A4 landscape;
            margin: 12mm;
        }

        html,
        body {
            margin: 0;
            padding: 0;
        }

        * {
            box-sizing: border-box;
            font-size: 16px;
            font-family: Arial, sans-serif;
        }

        /* Lebar area cetak A4 setelah margin: 210 - (12+12) = 186mm */
        .pdf-container {
            width: 273mm;
            margin: 0 auto;
            margin-top: 12mm;
        }

        img {
            max-width: 100%;
            height: auto;
            display: inline-block;
        }

        table.table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
        }

        table.table th,
        table.table td {
            border: 0.5px solid #000;
            text-align: left;
            padding: 8px;
            word-wrap: break-word;
            overflow-wrap: anywhere;
        }

        table.table th {
            text-align: center;
            font-weight: normal;
        }

        /* Header di tengah tanpa mendorong layout */
        table.center {
            margin: 0 auto;
            width: 100%;
            border-collapse: collapse;
        }

        table.center th,
        table.center td {
            border: 0;
            padding: 0;
        }

        hr {
            border: 0;
            border-top: 1px solid #000;
            margin: 8px 0 16px;
        }

        /* Tabel tanda tangan: 3 kolom proporsional yang pas area cetak */
        .sig {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
        }

        .sig td {
            border: 0;
            padding: 4px 0;
            text-align: center;
        }

        .sig .spacer {
            width: 18.6mm;
        }

        .sig .wide {
            width: calc((100% - 18.6mm)/2);
        }

        /* Page break utility */
        .page-break {
            page-break-before: always;
        }

        .sig {
            page-break-inside: avoid;
        }
    </style>
</head>

<body>
    <div class="pdf-container">

        <table class="center" style="text-align:center; margin-bottom: 20px;">
            <tr>
                <th rowspan="4" style="width: 30mm; text-align:right; padding-right:10mm;">
                    <img src="<?= base_url('/assets/img/logo-bpd.png') ?>" alt="logo" style="width: 26mm; height: 26mm;">
                </th>
                <th><b>BADAN PERMUSYAWARATAN DESA</b></th>
            </tr>
            <tr>
                <th><b>(BPD)</b></th>
            </tr>
            <tr>
                <th><b>DESA <?= $user->desaname ?> KECAMATAN <?= $user->kecamatanname ?></b></th>
            </tr>
            <tr>
                <th><b>KABUPATEN <?= $user->kabkotaname ?></b></th>
            </tr>
        </table>

        <b>
            <hr>
        </b>
        <div style="text-align: center; margin-bottom: 30px;">
            <p>
                BUKU DATA KEPUTUSAN KEPALA DESA
                <br>
                BADAN PERMUSYAWARATAN DESA (BPD)
            </p>
        </div>


        <table class="table">
            <colgroup>
                <col style="width: 10mm;"> <!-- No. Urut -->
                <col style="width: 35mm;"> <!-- Nomor dan Tanggal Keputusan Kepala Desa -->
                <col style="width: 40mm;"> <!-- Tentang -->
                <col style="width: 30mm;"> <!-- Uraian Singkat -->
                <col style="width: 30mm;"> <!-- Nomor dan Tanggal Dilaporkan -->
                <col style="width: 25mm;"> <!-- Ket. -->
            </colgroup>
            <tr>
                <th>No. Urut</th>
                <th>Nomor dan Tanggal Keputusan Kepala Desa</th>
                <th>Tentang</th>
                <th>Uraian Singkat</th>
                <th>Nomor dan Tanggal Dilaporkan</th>
                <th>Ket.</th>
            </tr>

            <?php foreach ($keputusankepaladesa as $key => $value) : ?>
                <tr>
                    <td><?= $value->nomor_urut ?? ($key + 1) ?></td>
                    <td>
                        <?php
                        $nomor_keputusan = $value->nomor_keputusan ?? '-';
                        $tanggal_keputusan = $value->tanggal_keputusan ? tgl_indo(date('Y-m-d', strtotime($value->tanggal_keputusan))) : '-';
                        echo wordwrap($nomor_keputusan, 12, '<br>', true) . '<br>' . wordwrap($tanggal_keputusan, 12, '<br>', true);
                        ?>
                    </td>
                    <td><?= wordwrap($value->tentang ?? '-', 18, '<br>', true) ?></td>
                    <td><?= wordwrap($value->uraian_singkat ?? '-', 15, '<br>', true) ?></td>
                    <td>
                        <?php
                        $nomor_dilaporkan = $value->nomor_dilaporkan ?? '-';
                        $tanggal_dilaporkan = $value->tanggal_dilaporkan ? tgl_indo(date('Y-m-d', strtotime($value->tanggal_dilaporkan))) : '-';
                        echo wordwrap($nomor_dilaporkan, 12, '<br>', true) . '<br>' . wordwrap($tanggal_dilaporkan, 12, '<br>', true);
                        ?>
                    </td>
                    <td><?= wordwrap($value->keterangan ?? '-', 12, '<br>', true) ?></td>
                </tr>
            <?php endforeach; ?>
        </table>

        <div class="page-break" style="margin-top: 28px;">
            <table class="sig">
                <tr>
                    <td class="wide">Mengetahui,</td>
                    <td class="spacer"></td>
                    <td class="wide"><?= $user->desaname ?>, <?= tgl_indo(getCurrentDate('Y-m-d')) ?></td>
                </tr>
                <tr>
                    <td class="wide">Kepala Desa</td>
                    <td class="spacer"></td>
                    <td class="wide">Sekretaris Desa</td>
                </tr>
            </table>

            <div style="height: 40mm;"></div>

            <table class="sig">
                <tr>
                    <td class="wide">(_______________)</td>
                    <td class="spacer"></td>
                    <td class="wide">(_______________)</td>
                </tr>
            </table>
        </div>
</body>

</html>
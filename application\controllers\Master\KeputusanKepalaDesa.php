<?php

use Dompdf\Dompdf;
use Dompdf\Options;

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property Keputusankepaladesas $keputusankepaladesas
 * @property Superadmins $admins
 * @property CI_Form_validation $form_validation
 * @property CI_Input $input
 * @property CI_Upload $upload
 */
class KeputusanKepalaDesa extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('Keputusankepaladesas', 'keputusankepaladesas');
        $this->load->model('Superadmins', 'admins');
    }

    public function index()
    {
        $startdate = getGet('startdate');
        $enddate = getGet('enddate');

        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $where = array();

        if ($startdate != null) {
            $where['DATE(a.tanggal_keputusan) >='] = $startdate;
        }

        if ($enddate != null) {
            $where['DATE(a.tanggal_keputusan) <='] = $enddate;
        }

        $data = array();
        $data['title'] = 'Keputusan Kepala Desa';
        $data['content'] = 'master/keputusankepaladesa/index';

        if (isVillages() || isBPD()) {
            $currentuser = getCurrentUser();
            $where['a.userid'] = $currentuser->id;
        }

        $data['keputusankepaladesa'] = $this->keputusankepaladesas->select('a.*, b.name AS createdname, b.username AS createdusername')
            ->join('msusers b', 'b.id = a.createdby')
            ->order_by('a.nomor_urut', 'ASC')
            ->result($where);
        $data['startdate'] = $startdate;
        $data['enddate'] = $enddate;

        return $this->load->view('master', $data);
    }

    public function export()
    {
        $startdate = getGet('startdate');
        $enddate = getGet('enddate');

        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $where = array();

        if ($startdate != null) {
            $where['DATE(a.tanggal_keputusan) >='] = $startdate;
        }

        if ($enddate != null) {
            $where['DATE(a.tanggal_keputusan) <='] = $enddate;
        }

        if (isVillages() || isBPD()) {
            $currentuser = getCurrentUser();
            $where['a.userid'] = $currentuser->id;
        }

        $keputusankepaladesa = $this->keputusankepaladesas->select('a.*, b.name AS createdname, b.username AS createdusername')
            ->join('msusers b', 'b.id = a.createdby')
            ->order_by('a.nomor_urut', 'ASC')
            ->result($where);

        $options = new Options();
        $options->set('isHtml5ParserEnabled', true);
        $options->set('isRemoteEnabled', true);
        $dompdf = new Dompdf($options);

        $dompdf->setPaper('A4', 'landscape');
        $dompdf->loadHtml($this->load->view('master/keputusankepaladesa/export', array(
            'keputusankepaladesa' => $keputusankepaladesa,
            'user' => $this->admins->get(array('id' => getCurrentIdUser()))->row()
        ), true));
        $dompdf->render();
        $dompdf->stream('Keputusan Kepala Desa.pdf', array("Attachment" => false));
    }

    public function add()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        if (!isVillages() && !isBPD()) {
            return redirect(base_url('master/keputusankepaladesa'));
        }

        $data = array();
        $data['title'] = 'Tambah Keputusan Kepala Desa';
        $data['content'] = 'master/keputusankepaladesa/add';
        $data['next_nomor_urut'] = $this->keputusankepaladesas->getNextNomorUrut(getCurrentUser()->id);
        $data['status_keterangan_options'] = $this->keputusankepaladesas->getStatusKeteranganOptions();

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Unauthorized');
        }

        if (!isVillages() && !isBPD()) {
            return JSONResponseDefault('FAILED', 'Akses ditolak');
        }

        // Validation rules
        $this->form_validation->set_rules('nomor_urut', 'Nomor Urut', 'required|integer');
        $this->form_validation->set_rules('nomor_keputusan', 'Nomor Keputusan', 'required');
        $this->form_validation->set_rules('tanggal_keputusan', 'Tanggal Keputusan', 'required');
        $this->form_validation->set_rules('tentang', 'Tentang', 'required');
        $this->form_validation->set_rules('uraian_singkat', 'Uraian Singkat', 'required');
        $this->form_validation->set_rules('nomor_dilaporkan', 'Nomor Dilaporkan', 'required');
        $this->form_validation->set_rules('tanggal_dilaporkan', 'Tanggal Dilaporkan', 'required');
        $this->form_validation->set_rules('status_keterangan', 'Status Keterangan', 'required');

        if ($this->form_validation->run() == FALSE) {
            return JSONResponseDefault('FAILED', validation_errors());
        }

        $data = $this->input->post();

        // Handle file upload
        if (!empty($_FILES['document']['name'])) {
            $config['upload_path'] = './uploads/';
            $config['allowed_types'] = 'pdf|doc|docx';
            $config['max_size'] = 10240; // 10MB
            $config['file_name'] = 'keputusan_' . time() . '_' . $_FILES['document']['name'];

            $this->load->library('upload', $config);

            if ($this->upload->do_upload('document')) {
                $upload_data = $this->upload->data();
                $data['document'] = $upload_data['file_name'];
            } else {
                return JSONResponseDefault('FAILED', 'Error upload file: ' . $this->upload->display_errors());
            }
        }

        // Set audit fields
        $data['userid'] = getCurrentUser()->id;
        $data['createdby'] = getCurrentUser()->id;
        $data['createddate'] = date('Y-m-d H:i:s');

        if ($this->keputusankepaladesas->insert($data)) {
            return JSONResponseDefault('SUCCESS', 'Data berhasil disimpan');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal menyimpan data');
        }
    }

    public function edit($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        if (!isVillages() && !isBPD()) {
            return redirect(base_url('master/keputusankepaladesa'));
        }

        $where = ['id' => $id];
        if (isVillages() || isBPD()) {
            $where['userid'] = getCurrentUser()->id;
        }

        $keputusankepaladesa = $this->keputusankepaladesas->get($where)->row();
        if (!$keputusankepaladesa) {
            show_404();
        }

        $data = array();
        $data['title'] = 'Edit Keputusan Kepala Desa';
        $data['content'] = 'master/keputusankepaladesa/edit';
        $data['keputusankepaladesa'] = $keputusankepaladesa;
        $data['status_keterangan_options'] = $this->keputusankepaladesas->getStatusKeteranganOptions();

        return $this->load->view('master', $data);
    }

    public function process_edit()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Unauthorized');
        }

        if (!isVillages() && !isBPD()) {
            return JSONResponseDefault('FAILED', 'Akses ditolak');
        }

        $id = $this->input->post('id');

        // Validation rules
        $this->form_validation->set_rules('nomor_urut', 'Nomor Urut', 'required|integer');
        $this->form_validation->set_rules('nomor_keputusan', 'Nomor Keputusan', 'required');
        $this->form_validation->set_rules('tanggal_keputusan', 'Tanggal Keputusan', 'required');
        $this->form_validation->set_rules('tentang', 'Tentang', 'required');
        $this->form_validation->set_rules('uraian_singkat', 'Uraian Singkat', 'required');
        $this->form_validation->set_rules('nomor_dilaporkan', 'Nomor Dilaporkan', 'required');
        $this->form_validation->set_rules('tanggal_dilaporkan', 'Tanggal Dilaporkan', 'required');
        $this->form_validation->set_rules('status_keterangan', 'Status Keterangan', 'required');

        if ($this->form_validation->run() == FALSE) {
            return JSONResponseDefault('FAILED', validation_errors());
        }

        $where = ['id' => $id];
        if (isVillages() || isBPD()) {
            $where['userid'] = getCurrentUser()->id;
        }

        $existing = $this->keputusankepaladesas->get($where)->row();
        if (!$existing) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $data = $this->input->post();
        unset($data['id']);

        // Handle file upload
        if (!empty($_FILES['document']['name'])) {
            $config['upload_path'] = './uploads/';
            $config['allowed_types'] = 'pdf|doc|docx';
            $config['max_size'] = 10240; // 10MB
            $config['file_name'] = 'keputusan_' . time() . '_' . $_FILES['document']['name'];

            $this->load->library('upload', $config);

            if ($this->upload->do_upload('document')) {
                $upload_data = $this->upload->data();
                $data['document'] = $upload_data['file_name'];

                // Delete old file if exists
                if ($existing->document && file_exists('./uploads/' . $existing->document)) {
                    unlink('./uploads/' . $existing->document);
                }
            } else {
                return JSONResponseDefault('FAILED', 'Error upload file: ' . $this->upload->display_errors());
            }
        }

        // Set audit fields
        $data['updatedby'] = getCurrentUser()->id;
        $data['updateddate'] = date('Y-m-d H:i:s');

        if ($this->keputusankepaladesas->update($data, $where)) {
            return JSONResponseDefault('SUCCESS', 'Data berhasil diperbarui');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal memperbarui data');
        }
    }

    public function delete($id)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Unauthorized');
        }

        if (!isVillages() && !isBPD()) {
            return JSONResponseDefault('FAILED', 'Akses ditolak');
        }

        $where = ['id' => $id];
        if (isVillages() || isBPD()) {
            $where['userid'] = getCurrentUser()->id;
        }

        $existing = $this->keputusankepaladesas->get($where)->row();
        if (!$existing) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        // Delete file if exists
        if ($existing->document && file_exists('./uploads/' . $existing->document)) {
            unlink('./uploads/' . $existing->document);
        }

        if ($this->keputusankepaladesas->delete($where)) {
            return JSONResponseDefault('SUCCESS', 'Data berhasil dihapus');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal menghapus data');
        }
    }
}

<?php

use Dompdf\Dompdf;
use Dompdf\Options;

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property Lembarandesas $lembarandesas
 */
class LembaranDesa extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('Lembarandesas', 'lembarandesas');
        $this->load->model('Superadmins', 'admins');
    }

    public function export()
    {
        $startdate = getGet('startdate');
        $enddate = getGet('enddate');

        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $where = array();
        if ($startdate != null) {
            $where['DATE(a.tanggal_diundangkan) >='] = $startdate;
        }
        if ($enddate != null) {
            $where['DATE(a.tanggal_diundangkan) <='] = $enddate;
        }

        $items = $this->lembarandesas->order_by('a.tanggal_diundangkan', 'DESC')->result($where);

        $options = new Options();
        $options->set('isHtml5ParserEnabled', true);
        $options->set('isRemoteEnabled', true);
        $dompdf = new Dompdf($options);
        $dompdf->setPaper('A4', 'landscape');

        $html = $this->load->view('master/lembarandesa/export', [
            'items' => $items,
            'user' => $this->admins->get(['id' => getCurrentIdUser()])->row(),
        ], true);

        $dompdf->loadHtml($html);
        $dompdf->render();
        $dompdf->stream('Lembaran Desa & Berita Desa.pdf', ["Attachment" => false]);
    }
}

<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>
<!-- [ breadcrumb ] start -->
<div class="page-header">
  <div class="page-block">
    <div class="row align-items-center">
      <div class="col-md-12">
        <ul class="breadcrumb">
          <li class="breadcrumb-item"><a href="<?= base_url(); ?>">Home</a></li>
          <li class="breadcrumb-item" aria-current="page">Buku Aparat Pemerintah Desa</li>
        </ul>
      </div>
      <div class="col-md-12">
        <div class="page-header-title">
          <h2 class="mb-0">Buku Aparat Pemerintah Desa</h2>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- [ breadcrumb ] end -->

<div class="row">
  <div class="col-md-12">
    <div class="card">
      <div class="card-header d-flex justify-content-between align-items-center">
        <div>
          <h4>Data Aparat Pemerintah Desa</h4>
        </div>
        <div>
          <a href="<?= base_url('master/aparatdesa/add') ?>" class="btn btn-success">
            <i class="fa fa-plus"></i>
            <span>Tambah</span>
          </a>
        </div>
      </div>
      <div class="card-body">
        <form action="<?= base_url('master/aparatdesa') ?>" method="GET" autocomplete="off">
          <div class="row align-items-end mb-4">
            <div class="col-md-3">
              <div class="mb-3">
                <label class="form-label">Tanggal Mulai</label>
                <input type="date" name="startdate" class="form-control" value="<?= $startdate ?>" />
              </div>
            </div>
            <div class="col-md-3">
              <div class="mb-3">
                <label class="form-label">Tanggal Selesai</label>
                <input type="date" name="enddate" class="form-control" value="<?= $enddate ?>" />
              </div>
            </div>
            <div class="col-md-3">
              <div class="mb-3">
                <button type="submit" class="btn btn-primary">Filter</button>
                <a href="<?= base_url('master/aparatdesa') ?>" class="btn btn-warning">Reset</a>
                <button type="button" class="btn btn-danger" onclick="exportData()">Export</button>
              </div>
            </div>
          </div>
        </form>

        <div class="table-responsive">
          <table class="table table-striped datatables">
            <thead>
              <tr>
                <th>No</th>
                <th>Nama</th>
                <th>NIAP</th>
                <th>NIP</th>
                <th>JK</th>
                <th>Jabatan</th>
                <th>Pendidikan</th>
                <th>SK Pengangkatan</th>
                <th>SK Pemberhentian</th>
                <th>Keterangan</th>
              </tr>
            </thead>
            <tbody>
              <?php foreach ($aparat as $key => $value): ?>
                <tr>
                  <td><?= $value->nomor_urut ?? ($key + 1) ?></td>
                  <td><?= htmlspecialchars($value->nama ?? '-') ?></td>
                  <td><?= $value->niap ?? '-' ?></td>
                  <td><?= $value->nip ?? '-' ?></td>
                  <td><?= $value->jenis_kelamin == 'Laki-laki' ? 'L' : ($value->jenis_kelamin == 'Perempuan' ? 'P' : '-') ?></td>
                  <td><?= htmlspecialchars($value->jabatan ?? '-') ?></td>
                  <td><?= htmlspecialchars($value->pendidikan_terakhir ?? '-') ?></td>
                  <td>
                    <?php 
                      $sk_angkat = '';
                      if ($value->no_sk_pengangkatan) $sk_angkat .= $value->no_sk_pengangkatan;
                      if ($value->tanggal_sk_pengangkatan) {
                        if ($sk_angkat) $sk_angkat .= '<br>';
                        $sk_angkat .= tgl_indo(date('Y-m-d', strtotime($value->tanggal_sk_pengangkatan)));
                      }
                      echo $sk_angkat ?: '-';
                    ?>
                  </td>
                  <td>
                    <?php 
                      $sk_berhenti = '';
                      if ($value->no_sk_pemberhentian) $sk_berhenti .= $value->no_sk_pemberhentian;
                      if ($value->tanggal_sk_pemberhentian) {
                        if ($sk_berhenti) $sk_berhenti .= '<br>';
                        $sk_berhenti .= tgl_indo(date('Y-m-d', strtotime($value->tanggal_sk_pemberhentian)));
                      }
                      echo $sk_berhenti ?: '-';
                    ?>
                  </td>
                  <td><?= htmlspecialchars($value->keterangan ?? '-') ?></td>
                </tr>
              <?php endforeach; ?>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
function exportData() {
  const startdate = document.querySelector('input[name="startdate"]').value || '';
  const enddate = document.querySelector('input[name="enddate"]').value || '';
  const url = `<?= base_url('master/aparatdesa/export') ?>?startdate=${encodeURIComponent(startdate)}&enddate=${encodeURIComponent(enddate)}`;
  window.open(url, '_blank');
}
</script>


<?php

use Dompdf\Dompdf;
use Dompdf\Options;


defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property Peraturandesas $peraturandesas
 * @property Superadmins $admins
 * @property CI_Form_validation $form_validation
 * @property CI_Input $input
 * @property CI_Upload $upload
 */
class PeraturanDesa extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('Peraturandesas', 'peraturandesas');
        $this->load->model('Superadmins', 'admins');
    }

    public function index()
    {
        $startdate = getGet('startdate');
        $enddate = getGet('enddate');

        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $where = array();

        if ($startdate != null) {
            $where['DATE(a.date) >='] = $startdate;
        }

        if ($enddate != null) {
            $where['DATE(a.date) <='] = $enddate;
        }

        $data = array();
        $data['title'] = 'Peraturan Desa';
        $data['content'] = 'master/peraturandesa/index';

        if (isVillages() || isBPD()) {
            $currentuser = getCurrentUser();

            $bpd = $this->admins->get(array('desaid' => $currentuser->desaid, 'role' => 'BPD'))->result();

            $bpdid = array();
            foreach ($bpd as $value) {
                $bpdid[] = $value->id;
            }

            $this->peraturandesas->where_in('userid', $bpdid);
        }

        $data['peraturandesa'] = $this->peraturandesas->select('a.*, b.name AS createdname, b.username AS createdusername')
            ->join('msusers b', 'b.id = a.createdby')
            ->order_by('a.nomor_urut', 'ASC')
            ->result($where);
        $data['startdate'] = $startdate;
        $data['enddate'] = $enddate;

        return $this->load->view('master', $data);
    }

    public function export()
    {
        $startdate = getGet('startdate');
        $enddate = getGet('enddate');

        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $where = array();

        if ($startdate != null) {
            $where['DATE(a.date) >='] = $startdate;
        }

        if ($enddate != null) {
            $where['DATE(a.date) <='] = $enddate;
        }

        if (isVillages() || isBPD()) {
            $currentuser = getCurrentUser();

            $bpd = $this->admins->get(array('desaid' => $currentuser->desaid, 'role' => 'BPD'))->result();

            $bpdid = array();
            foreach ($bpd as $value) {
                $bpdid[] = $value->id;
            }

            $this->peraturandesas->where_in('userid', $bpdid);
        }

        $peraturandesa = $this->peraturandesas->order_by('a.date', 'DESC')->result($where);

        $options = new Options();
        $options->set('isHtml5ParserEnabled', true);
        $options->set('isRemoteEnabled', true);
        $dompdf = new Dompdf($options);

        $dompdf->setPaper('A4', 'landscape');
        $dompdf->loadHtml($this->load->view('master/peraturandesa/export', array(
            'peraturandesa' => $peraturandesa,
            'user' => $this->admins->get(array('id' => getCurrentIdUser()))->row()
        ), true));
        $dompdf->render();
        $dompdf->stream('Peraturan Desa.pdf', array("Attachment" => false));
    }

    public function lembarandesa()
    {
        $startdate = getGet('startdate');
        $enddate = getGet('enddate');

        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $where = array();
        $where['a.jenis_peraturan'] = 'Peraturan Desa';

        if ($startdate != null) {
            $where['DATE(a.tanggal_lembaran_desa) >='] = $startdate;
        }

        if ($enddate != null) {
            $where['DATE(a.tanggal_lembaran_desa) <='] = $enddate;
        }

        $data = array();
        $data['title'] = 'Lembaran Desa';
        $data['content'] = 'master/peraturandesa/lembarandesa/index';

        if (isVillages() || isBPD()) {
            $currentuser = getCurrentUser();

            $bpd = $this->admins->get(array('desaid' => $currentuser->desaid, 'role' => 'BPD'))->result();
            $bpdid = array();
            foreach ($bpd as $value) {
                $bpdid[] = $value->id;
            }
            $this->peraturandesas->where_in('userid', $bpdid);
        }

        $data['lembarandesa'] = $this->peraturandesas->select('a.*, b.name AS createdname, b.username AS createdusername')
            ->join('msusers b', 'b.id = a.createdby')
            ->order_by('a.nomor_urut', 'ASC')
            ->result($where);
        $data['startdate'] = $startdate;
        $data['enddate'] = $enddate;

        return $this->load->view('master', $data);
    }

    public function export_lembarandesa()
    {
        $startdate = getGet('startdate');
        $enddate = getGet('enddate');

        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $where = array();
        $where['a.jenis_peraturan'] = 'Peraturan Desa';

        if ($startdate != null) {
            $where['DATE(a.tanggal_lembaran_desa) >='] = $startdate;
        }

        if ($enddate != null) {
            $where['DATE(a.tanggal_lembaran_desa) <='] = $enddate;
        }

        if (isVillages() || isBPD()) {
            $currentuser = getCurrentUser();
            $bpd = $this->admins->get(array('desaid' => $currentuser->desaid, 'role' => 'BPD'))->result();
            $bpdid = array();
            foreach ($bpd as $value) {
                $bpdid[] = $value->id;
            }
            $this->peraturandesas->where_in('userid', $bpdid);
        }

        $lembarandesa = $this->peraturandesas->order_by('a.tanggal_lembaran_desa', 'DESC')->result($where);

        $options = new Options();
        $options->set('isHtml5ParserEnabled', true);
        $options->set('isRemoteEnabled', true);
        $dompdf = new Dompdf($options);

        $dompdf->setPaper('A4', 'landscape');
        $dompdf->loadHtml($this->load->view('master/peraturandesa/lembarandesa/export', array(
            'lembarandesa' => $lembarandesa,
            'user' => $this->admins->get(array('id' => getCurrentIdUser()))->row()
        ), true));
        $dompdf->render();
        $dompdf->stream('Lembaran Desa.pdf', array("Attachment" => false));
    }

    public function beritadesa()
    {
        $startdate = getGet('startdate');
        $enddate = getGet('enddate');

        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $where = array();
        $this->peraturandesas->where_in('a.jenis_peraturan', ['Peraturan Kepala Desa', 'Peraturan Bersama Kepala Desa']);

        if ($startdate != null) {
            $where['DATE(a.tanggal_berita_desa) >='] = $startdate;
        }

        if ($enddate != null) {
            $where['DATE(a.tanggal_berita_desa) <='] = $enddate;
        }

        $data = array();
        $data['title'] = 'Berita Desa';
        $data['content'] = 'master/peraturandesa/beritadesa/index';

        if (isVillages() || isBPD()) {
            $currentuser = getCurrentUser();
            $bpd = $this->admins->get(array('desaid' => $currentuser->desaid, 'role' => 'BPD'))->result();
            $bpdid = array();
            foreach ($bpd as $value) {
                $bpdid[] = $value->id;
            }
            $this->peraturandesas->where_in('userid', $bpdid);
        }

        $data['beritadesa'] = $this->peraturandesas->select('a.*, b.name AS createdname, b.username AS createdusername')
            ->join('msusers b', 'b.id = a.createdby')
            ->order_by('a.nomor_urut', 'ASC')
            ->result($where);
        $data['startdate'] = $startdate;
        $data['enddate'] = $enddate;

        return $this->load->view('master', $data);
    }

    public function export_beritadesa()
    {
        $startdate = getGet('startdate');
        $enddate = getGet('enddate');

        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $where = array();
        $this->peraturandesas->where_in('a.jenis_peraturan', ['Peraturan Kepala Desa', 'Peraturan Bersama Kepala Desa']);

        if ($startdate != null) {
            $where['DATE(a.tanggal_berita_desa) >='] = $startdate;
        }

        if ($enddate != null) {
            $where['DATE(a.tanggal_berita_desa) <='] = $enddate;
        }

        if (isVillages() || isBPD()) {
            $currentuser = getCurrentUser();
            $bpd = $this->admins->get(array('desaid' => $currentuser->desaid, 'role' => 'BPD'))->result();
            $bpdid = array();
            foreach ($bpd as $value) {
                $bpdid[] = $value->id;
            }
            $this->peraturandesas->where_in('userid', $bpdid);
        }

        $beritadesa = $this->peraturandesas->order_by('a.tanggal_berita_desa', 'DESC')->result($where);

        $options = new Options();
        $options->set('isHtml5ParserEnabled', true);
        $options->set('isRemoteEnabled', true);
        $dompdf = new Dompdf($options);

        $dompdf->setPaper('A4', 'landscape');
        $dompdf->loadHtml($this->load->view('master/peraturandesa/beritadesa/export', array(
            'beritadesa' => $beritadesa,
            'user' => $this->admins->get(array('id' => getCurrentIdUser()))->row()
        ), true));
        $dompdf->render();
        $dompdf->stream('Berita Desa.pdf', array("Attachment" => false));
    }



    public function add()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        if (!isBPD()) {
            return redirect(base_url('master/peraturandesa'));
        }

        $data = array();
        $data['title'] = 'Tambah Peraturan Desa';
        $data['content'] = 'master/peraturandesa/add';
        $data['next_nomor_urut'] = $this->peraturandesas->getNextNomorUrut(getCurrentUser()->id);
        $data['jenis_peraturan_options'] = $this->peraturandesas->getJenisPeraturanOptions();
        $data['status_keterangan_options'] = $this->peraturandesas->getStatusKeteranganOptions();

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Unauthorized');
        }

        if (!isBPD()) {
            return JSONResponseDefault('FAILED', 'Akses ditolak');
        }

        // Validation rules
        $this->form_validation->set_rules('nomor_urut', 'Nomor Urut', 'required|integer');
        $this->form_validation->set_rules('jenis_peraturan', 'Jenis Peraturan', 'required');
        $this->form_validation->set_rules('nomor_ditetapkan', 'Nomor Ditetapkan', 'required');
        $this->form_validation->set_rules('tanggal_ditetapkan', 'Tanggal Ditetapkan', 'required');
        $this->form_validation->set_rules('tentang', 'Tentang', 'required');
        $this->form_validation->set_rules('uraiansingkat', 'Uraian Singkat', 'required');
        $this->form_validation->set_rules('status_keterangan', 'Status Keterangan', 'required');

        // Conditional validation based on jenis peraturan
        $jenis_peraturan = $this->input->post('jenis_peraturan');

        if ($jenis_peraturan === 'Peraturan Desa') {
            $this->form_validation->set_rules('tanggalkesepakatan', 'Tanggal Kesepakatan', 'required');
            $this->form_validation->set_rules('nomor_lembaran_desa', 'Nomor Lembaran Desa', 'required');
            $this->form_validation->set_rules('tanggal_lembaran_desa', 'Tanggal Lembaran Desa', 'required');
        } elseif (in_array($jenis_peraturan, ['Peraturan Kepala Desa', 'Peraturan Bersama Kepala Desa'])) {
            $this->form_validation->set_rules('nomor_berita_desa', 'Nomor Berita Desa', 'required');
            $this->form_validation->set_rules('tanggal_berita_desa', 'Tanggal Berita Desa', 'required');
        }

        if ($this->form_validation->run() == FALSE) {
            return JSONResponseDefault('FAILED', validation_errors());
        }

        $data = $this->input->post();

        // Handle file upload
        if (!empty($_FILES['document']['name'])) {
            $config['upload_path'] = './uploads/';
            $config['allowed_types'] = 'pdf|doc|docx';
            $config['max_size'] = 10240; // 10MB
            $config['file_name'] = 'peraturan_' . time() . '_' . $_FILES['document']['name'];

            $this->load->library('upload', $config);

            if ($this->upload->do_upload('document')) {
                $upload_data = $this->upload->data();
                $data['document'] = $upload_data['file_name'];
            } else {
                return JSONResponseDefault('FAILED', 'Error upload file: ' . $this->upload->display_errors());
            }
        }

        // Set audit fields
        $data['userid'] = getCurrentUser()->id;
        $data['createdby'] = getCurrentUser()->id;
        $data['createddate'] = date('Y-m-d H:i:s');
        $data['date'] = $data['tanggal_ditetapkan']; // Use tanggal ditetapkan as main date

        // Clean conditional fields based on jenis peraturan
        if ($jenis_peraturan !== 'Peraturan Desa') {
            unset($data['tanggalkesepakatan']);
            unset($data['nomor_lembaran_desa']);
            unset($data['tanggal_lembaran_desa']);
        }

        if (!in_array($jenis_peraturan, ['Peraturan Kepala Desa', 'Peraturan Bersama Kepala Desa'])) {
            unset($data['nomor_berita_desa']);
            unset($data['tanggal_berita_desa']);
        }

        if ($this->peraturandesas->insert($data)) {
            return JSONResponseDefault('SUCCESS', 'Data berhasil disimpan');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal menyimpan data');
        }
    }

    public function edit($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        if (!isBPD()) {
            return redirect(base_url('master/peraturandesa'));
        }

        $where = ['id' => $id];
        if (isVillages() || isBPD()) {
            $where['userid'] = getCurrentUser()->id;
        }

        $peraturandesa = $this->peraturandesas->get($where)->row();
        if (!$peraturandesa) {
            show_404();
        }

        $data = array();
        $data['title'] = 'Edit Peraturan Desa';
        $data['content'] = 'master/peraturandesa/edit';
        $data['peraturandesa'] = $peraturandesa;
        $data['jenis_peraturan_options'] = $this->peraturandesas->getJenisPeraturanOptions();
        $data['status_keterangan_options'] = $this->peraturandesas->getStatusKeteranganOptions();

        return $this->load->view('master', $data);
    }

    public function process_edit()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Unauthorized');
        }

        if (!isBPD()) {
            return JSONResponseDefault('FAILED', 'Akses ditolak');
        }

        $id = $this->input->post('id');

        // Validation rules
        $this->form_validation->set_rules('nomor_urut', 'Nomor Urut', 'required|integer');
        $this->form_validation->set_rules('jenis_peraturan', 'Jenis Peraturan', 'required');
        $this->form_validation->set_rules('nomor_ditetapkan', 'Nomor Ditetapkan', 'required');
        $this->form_validation->set_rules('tanggal_ditetapkan', 'Tanggal Ditetapkan', 'required');
        $this->form_validation->set_rules('tentang', 'Tentang', 'required');
        $this->form_validation->set_rules('uraiansingkat', 'Uraian Singkat', 'required');
        $this->form_validation->set_rules('status_keterangan', 'Status Keterangan', 'required');

        // Conditional validation based on jenis peraturan
        $jenis_peraturan = $this->input->post('jenis_peraturan');

        if ($jenis_peraturan === 'Peraturan Desa') {
            $this->form_validation->set_rules('tanggalkesepakatan', 'Tanggal Kesepakatan', 'required');
            $this->form_validation->set_rules('nomor_lembaran_desa', 'Nomor Lembaran Desa', 'required');
            $this->form_validation->set_rules('tanggal_lembaran_desa', 'Tanggal Lembaran Desa', 'required');
        } elseif (in_array($jenis_peraturan, ['Peraturan Kepala Desa', 'Peraturan Bersama Kepala Desa'])) {
            $this->form_validation->set_rules('nomor_berita_desa', 'Nomor Berita Desa', 'required');
            $this->form_validation->set_rules('tanggal_berita_desa', 'Tanggal Berita Desa', 'required');
        }

        if ($this->form_validation->run() == FALSE) {
            return JSONResponseDefault('FAILED', validation_errors());
        }

        $where = ['id' => $id];
        if (isVillages() || isBPD()) {
            $where['userid'] = getCurrentUser()->id;
        }

        $existing = $this->peraturandesas->get($where)->row();
        if (!$existing) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $data = $this->input->post();
        unset($data['id']);

        // Handle file upload
        if (!empty($_FILES['document']['name'])) {
            $config['upload_path'] = './uploads/';
            $config['allowed_types'] = 'pdf|doc|docx';
            $config['max_size'] = 10240; // 10MB
            $config['file_name'] = 'peraturan_' . time() . '_' . $_FILES['document']['name'];

            $this->load->library('upload', $config);

            if ($this->upload->do_upload('document')) {
                $upload_data = $this->upload->data();
                $data['document'] = $upload_data['file_name'];

                // Delete old file if exists
                if ($existing->document && file_exists('./uploads/' . $existing->document)) {
                    unlink('./uploads/' . $existing->document);
                }
            } else {
                return JSONResponseDefault('FAILED', 'Error upload file: ' . $this->upload->display_errors());
            }
        }

        // Set audit fields
        $data['updatedby'] = getCurrentUser()->id;
        $data['updateddate'] = date('Y-m-d H:i:s');
        $data['date'] = $data['tanggal_ditetapkan']; // Use tanggal ditetapkan as main date

        // Clean conditional fields based on jenis peraturan
        if ($jenis_peraturan !== 'Peraturan Desa') {
            $data['tanggalkesepakatan'] = null;
            $data['nomor_lembaran_desa'] = null;
            $data['tanggal_lembaran_desa'] = null;
        }

        if (!in_array($jenis_peraturan, ['Peraturan Kepala Desa', 'Peraturan Bersama Kepala Desa'])) {
            $data['nomor_berita_desa'] = null;
            $data['tanggal_berita_desa'] = null;
        }

        if ($this->peraturandesas->update($data, $where)) {
            return JSONResponseDefault('SUCCESS', 'Data berhasil diperbarui');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal memperbarui data');
        }
    }

    public function delete($id)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Unauthorized');
        }

        if (!isBPD()) {
            return JSONResponseDefault('FAILED', 'Akses ditolak');
        }

        $where = ['id' => $id];
        if (isVillages() || isBPD()) {
            $where['userid'] = getCurrentUser()->id;
        }

        $existing = $this->peraturandesas->get($where)->row();
        if (!$existing) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        // Delete file if exists
        if ($existing->document && file_exists('./uploads/' . $existing->document)) {
            unlink('./uploads/' . $existing->document);
        }

        if ($this->peraturandesas->delete($where)) {
            return JSONResponseDefault('SUCCESS', 'Data berhasil dihapus');
        } else {
            return JSONResponseDefault('FAILED', 'Gagal menghapus data');
        }
    }
}

<?php

use Spipu\Html2Pdf\Html2Pdf;

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property AparatDesas $aparatdesas
 * @property Superadmins $admins
 */
class AparatDesa extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('AparatDesas', 'aparatdesas');
        $this->load->model('Superadmins', 'admins');
    }

    public function index()
    {
        $startdate = getGet('startdate');
        $enddate = getGet('enddate');

        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $where = array();
        if ($startdate != null) {
            $where['DATE(a.createddate) >='] = $startdate;
        }
        if ($enddate != null) {
            $where['DATE(a.createddate) <='] = $enddate;
        }

        if (isVillages() || isBPD()) {
            $currentuser = getCurrentUser();
            $where['a.userid'] = $currentuser->id;
        }

        $data = array();
        $data['title'] = 'Buku Aparat Pemerintah Desa';
        $data['content'] = 'master/aparatdesa/index';
        $data['aparat'] = $this->aparatdesas->select('a.*, b.name AS createdname, b.username AS createdusername')
            ->join('msusers b', 'b.id = a.createdby', 'left')
            ->order_by('a.nomor_urut', 'ASC')
            ->result($where);
        $data['startdate'] = $startdate;
        $data['enddate'] = $enddate;

        return $this->load->view('master', $data);
    }

    public function export()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $startdate = getGet('startdate');
        $enddate   = getGet('enddate');

        $where = array();
        if ($startdate != null) {
            $where['DATE(a.createddate) >='] = $startdate;
        }
        if ($enddate != null) {
            $where['DATE(a.createddate) <='] = $enddate;
        }

        if (isVillages() || isBPD()) {
            $currentuser = getCurrentUser();
            $where['a.userid'] = $currentuser->id;
        }

        $aparat = $this->aparatdesas->select('a.*, b.name AS createdname, b.username AS createdusername')
            ->join('msusers b', 'b.id = a.createdby', 'left')
            ->order_by('a.nomor_urut', 'ASC')
            ->result($where);

        $html2pdf = new Html2Pdf('L');
        $html2pdf->writeHTML($this->load->view('master/aparatdesa/export_v2', array(
            'aparat' => $aparat,
            'user' => $this->admins->get(array('id' => getCurrentIdUser()))->row()
        ), true));
        $html2pdf->output('Buku Aparat Pemerintah Desa.pdf');
    }

    public function statistics()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Unauthorized');
        }

        $userid = null;
        if (isVillages() || isBPD()) {
            $userid = getCurrentUser()->id;
        }

        $statistics = $this->aparatdesas->getStatistics($userid);

        return JSONResponseDefault('SUCCESS', $statistics);
    }

    public function search()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Unauthorized');
        }

        $keyword = $this->input->get('q');
        if (empty($keyword)) {
            return JSONResponseDefault('SUCCESS', []);
        }

        $userid = null;
        if (isVillages() || isBPD()) {
            $userid = getCurrentUser()->id;
        }

        $results = $this->aparatdesas->search($keyword, $userid);

        $data = [];
        foreach ($results as $row) {
            $data[] = [
                'id' => $row->id,
                'nama' => $row->nama,
                'niap' => $row->niap,
                'nip' => $row->nip,
                'jabatan' => $row->jabatan,
                'text' => $row->nama . ' - ' . $row->jabatan . ' (' . ($row->niap ?: $row->nip) . ')'
            ];
        }

        return JSONResponseDefault('SUCCESS', $data);
    }

    public function dashboard()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $userid = null;
        if (isVillages() || isBPD()) {
            $userid = getCurrentUser()->id;
        }

        $data = array();
        $data['title'] = 'Dashboard Buku Aparat Pemerintah Desa';
        $data['content'] = 'master/aparatdesa/dashboard';
        $data['statistics'] = $this->aparatdesas->getStatistics($userid);
        $data['recent_items'] = $this->aparatdesas->select('*')
            ->where($userid ? ['userid' => $userid] : [])
            ->order_by('createddate', 'DESC')
            ->limit(10)
            ->result();

        return $this->load->view('master', $data);
    }

    public function chart_data()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Unauthorized');
        }

        $type = $this->input->get('type', true);
        $userid = null;
        if (isVillages() || isBPD()) {
            $userid = getCurrentUser()->id;
        }

        switch ($type) {
            case 'by_gender':
                $statistics = $this->aparatdesas->getStatistics($userid);
                $data = $statistics['by_gender'];
                break;
            case 'by_jabatan':
                $statistics = $this->aparatdesas->getStatistics($userid);
                $data = $statistics['by_jabatan'];
                break;
            case 'by_pendidikan':
                $statistics = $this->aparatdesas->getStatistics($userid);
                $data = $statistics['by_pendidikan'];
                break;
            default:
                $data = [];
        }

        return JSONResponseDefault('SUCCESS', $data);
    }

    public function upload_file()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Unauthorized');
        }

        if (empty($_FILES['file']['name'])) {
            return JSONResponseDefault('FAILED', 'File harus dipilih');
        }

        $config['upload_path'] = './uploads/aparatdesa/';
        $config['allowed_types'] = 'pdf|doc|docx|jpg|jpeg|png';
        $config['max_size'] = 5120; // 5MB
        $config['file_name'] = 'keputusan_' . time() . '_' . $_FILES['file']['name'];

        // Create directory if not exists
        if (!is_dir('./uploads/aparatdesa/')) {
            mkdir('./uploads/aparatdesa/', 0755, true);
        }

        $this->load->library('upload', $config);

        if (!$this->upload->do_upload('file')) {
            return JSONResponseDefault('FAILED', 'Error upload file: ' . $this->upload->display_errors());
        }

        $upload_data = $this->upload->data();

        return JSONResponseDefault('SUCCESS', [
            'file_name' => $upload_data['file_name'],
            'file_path' => base_url('uploads/aparatdesa/' . $upload_data['file_name'])
        ]);
    }
}

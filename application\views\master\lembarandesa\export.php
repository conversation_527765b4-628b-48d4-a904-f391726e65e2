<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Export Lembaran Desa & Berita Desa</title>

    <style>
        @page {
            size: A4 landscape;
            margin: 12mm;
        }

        html, body { margin: 0; padding: 0; }

        * {
            box-sizing: border-box;
            font-size: 16px;
            font-family: Arial, sans-serif;
        }

        .pdf-container {
            width: 273mm;
            margin: 0 auto;
            margin-top: 12mm;
        }

        img { max-width: 100%; height: auto; display: inline-block; }

        table.table { width: 100%; border-collapse: collapse; table-layout: fixed; }
        table.table th, table.table td {
            border: 0.5px solid #000; text-align: left; padding: 8px; word-wrap: break-word; overflow-wrap: anywhere;
        }
        table.table th { text-align: center; font-weight: normal; }

        table.center { margin: 0 auto; width: 100%; border-collapse: collapse; }
        table.center th, table.center td { border: 0; padding: 0; }

        hr { border: 0; border-top: 1px solid #000; margin: 8px 0 16px; }

        .sig { width: 100%; border-collapse: collapse; table-layout: fixed; page-break-inside: avoid; }
        .sig td { border: 0; padding: 4px 0; text-align: center; }
        .sig .spacer { width: 18.6mm; }
        .sig .wide { width: calc((100% - 18.6mm)/2); }

        .page-break { page-break-before: always; }
    </style>
</head>

<body>
<div class="pdf-container">

    <table class="center" style="text-align:center; margin-bottom : 30px;">
        <tr>
            <th rowspan="4"><img src="<?= base_url('/assets/img/logo-bpd.png') ?>" width="100px;" height="100px;" style="margin-right:50px;" alt=""></th>
            <th><b>PEMERINTAH DESA</b></th>
        </tr>
        <tr>
            <th><b>DESA <?= $user->desaname ?> KECAMATAN <?= $user->kecamatanname ?></b></th>
        </tr>
        <tr>
            <th><b>KABUPATEN <?= $user->kabkotaname ?></b></th>
        </tr>
        <tr>
            <th><b>PROVINSI</b></th>
        </tr>
    </table>

    <b><hr></b>
    <div style="text-align: center; margin-bottom: 30px;">
        <p>
            BUKU LEMBARAN DESA & BERITA DESA<br>
            PEMERINTAH DESA <?= $user->desaname ?>
        </p>
    </div>

    <table class="table">
        <colgroup>
            <col style="width: 7.8mm;">   <!-- No. Urut (≈40px) -->
            <col style="width: 27.4mm;">  <!-- Jenis Peraturan (≈140px) -->
            <col style="width: 23.5mm;">  <!-- Nomor Ditetapkan (≈120px) -->
            <col style="width: 15.7mm;">  <!-- Tgl Ditetapkan (≈80px) -->
            <col style="width: 29.4mm;">  <!-- Tentang (≈150px) -->
            <col style="width: 15.7mm;">  <!-- Tgl Diundangkan (≈80px) -->
            <col style="width: 23.5mm;">  <!-- Nomor Diundangkan (≈120px) -->
            <col style="width: 19.6mm;">  <!-- Keterangan (≈100px) -->
        </colgroup>
        <tr>
            <th width="40">No.</th>
            <th width="140">Jenis Peraturan</th>
            <th width="120">Nomor Ditetapkan</th>
            <th width="80">Tgl Ditetapkan</th>
            <th width="150">Tentang</th>
            <th width="80">Tgl Diundangkan</th>
            <th width="120">Nomor Diundangkan</th>
            <th width="100">Keterangan</th>
        </tr>

        <?php foreach ($items as $key => $value) : ?>
            <tr>
                <td width="40"><?= $key + 1 ?></td>
                <td width="140"><?= wordwrap($value->jenis ?? '-', 18, '<br>', true) ?></td>
                <td width="120"><?= wordwrap($value->nomor_ditetapkan ?? '-', 16, '<br>', true) ?></td>
                <td width="80"><?= $value->tanggal_ditetapkan ? tgl_indo(date('Y-m-d', strtotime($value->tanggal_ditetapkan))) : '-' ?></td>
                <td width="150"><?= wordwrap($value->tentang ?? '-', 20, '<br>', true) ?></td>
                <td width="80"><?= $value->tanggal_diundangkan ? tgl_indo(date('Y-m-d', strtotime($value->tanggal_diundangkan))) : '-' ?></td>
                <td width="120"><?= wordwrap($value->nomor_diundangkan ?? '-', 16, '<br>', true) ?></td>
                <td width="100"><?= wordwrap($value->keterangan ?? '-', 14, '<br>', true) ?></td>
            </tr>
        <?php endforeach; ?>
    </table>

    <div class="page-break" style="margin-top: 28px;">
        <table class="sig">
            <tr>
                <td class="wide">Mengetahui,</td>
                <td class="spacer"></td>
                <td class="wide"><?= $user->desaname ?>, <?= tgl_indo(getCurrentDate('Y-m-d')) ?></td>
            </tr>
            <tr>
                <td class="wide">Kepala Desa</td>
                <td class="spacer"></td>
                <td class="wide">Sekretaris Desa</td>
            </tr>
        </table>

        <div style="height: 40mm;"></div>

        <table class="sig">
            <tr>
                <td class="wide">(___<?= str_repeat('_', 12) ?>___)</td>
                <td class="spacer"></td>
                <td class="wide">(___<?= str_repeat('_', 12) ?>___)</td>
            </tr>
        </table>
    </div>

</div>
</body>
</html>


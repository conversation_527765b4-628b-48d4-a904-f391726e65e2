<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<!-- [ breadcrumb ] start -->
<div class="page-header">
    <div class="page-block">
        <div class="row align-items-center">
            <div class="col-md-12">
                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= base_url(); ?>">Home</a></li>
                    <li class="breadcrumb-item" aria-current="page"><?= $title ?></li>
                </ul>
            </div>

            <div class="col-md-12">
                <div class="page-header-title">
                    <h2 class="mb-0"><?= $title ?></h2>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- [ breadcrumb ] end -->

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <h4><?= isset($element['title']) ? $element['title'] : 'Data ' . $title ?></h4>
                </div>

                <div>
                    <?php if (!isset($element['buttons'])) : ?>
                        <a href="<?= base_url(uri_string() . '/add') ?>" class="btn btn-primary">
                            <i class="fa fa-plus"></i>
                            <span>Tambah</span>
                        </a>
                    <?php else : ?>
                        <?php foreach ($element['buttons'] as $key => $value) : ?>
                            <?php
                            $attr = isset($value['attr']) ? $value['attr'] : array();

                            $attribute = "";
                            foreach ($attr as $k => $v) {
                                $attribute .= "$k=\"$v\" ";
                            }
                            ?>

                            <?php if ($value['type'] == 'a') : ?>
                                <a href="<?= $value['href'] ?>" class="<?= $value['class'] ?>" <?= $attribute ?>>
                                    <?php if (isset($value['icon'])) : ?>
                                        <i class="<?= $value['icon'] ?>"></i>
                                    <?php endif; ?>

                                    <span><?= $value['text'] ?></span>
                                </a>
                            <?php elseif ($value['type'] == 'button') : ?>
                                <button type="button" class="<?= $value['class'] ?>" <?= $attribute ?>>
                                    <?php if (isset($value['icon'])) : ?>
                                        <i class="<?= $value['icon'] ?>"></i>
                                    <?php endif; ?>

                                    <span><?= $value['text'] ?></span>
                                </button>
                            <?php elseif ($value['type'] == 'span') : ?>
                                <span><?= $value['text'] ?></span>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>

            <div class="card-body">
                <?php if (isset($element['filters'])) : ?>
                    <form id="filters-form" action="<?= $element['filters']['form_action'] ?>" method="<?= isset($element['filters']['method']) ? $element['filters']['method'] : 'GET' ?>">
                        <div class="row align-items-end">
                            <?php foreach ($element['filters']['data'] as $key => $value) : ?>
                                <?php
                                $attr = isset($value['attr']) ? $value['attr'] : array();

                                $attribute = "";
                                foreach ($attr as $k => $v) {
                                    $attribute .= "$k='$v' ";
                                }
                                ?>

                                <div class="<?= $value['parent_class'] ?>">
                                    <div class="<?= isset($value['input_type']) ? ($value['input_type'] == 'hidden' ? 'd-none' : 'mb-3') : 'mb-3' ?>">
                                        <?php if (isset($value['label'])) : ?>
                                            <label for=""><?= $value['label'] ?> <?= isset($value['required']) ? '<span class="text-danger">*</span>' : null ?></label>
                                        <?php endif; ?>

                                        <?php if (isset($value['type'])) : ?>
                                            <?php if ($value['type'] == 'input') : ?>
                                                <input type="<?= $value['input_type'] ?>" name="<?= $value['variable'] ?>" class="form-control" placeholder="<?= isset($value['placeholder']) ? $value['placeholder'] : null ?>" value="<?= isset($value['default']) ? $value['default'] : null ?>" <?= isset($value['required']) ? 'required' : null ?> <?= $attribute ?>>
                                            <?php elseif ($value['type'] == 'textarea') : ?>
                                                <textarea name="<?= $value['variable'] ?>" class="form-control" placeholder="<?= $value['placeholder'] ?>" <?= isset($value['required']) ? 'required' : null ?> <?= $attribute ?>></textarea>
                                            <?php elseif ($value['type'] == 'select') : ?>
                                                <select name="<?= $value['variable'] ?>" class="form-control" <?= isset($value['required']) ? 'required' : null ?> <?= $attribute ?>>
                                                    <?php if (isset($value['defaultvalue']['data'])) : ?>
                                                        <?php if (isset($value['prefixvalue'])) : ?>
                                                            <option value="<?= $value['prefixvalue']['value'] ?>"><?= $value['prefixvalue']['text'] ?></option>
                                                        <?php endif; ?>

                                                        <?php foreach ($value['defaultvalue']['data'] as $k => $v) : ?>
                                                            <?php if (!isset($value['defaultvalue']['type']) || $value['defaultvalue']['type'] == 'object') : ?>
                                                                <option value="<?= $v->{$value['defaultvalue']['value']} ?>" <?= (isset($value['defaultvalue']['selected']) && $value['defaultvalue']['selected'] == $v->{$value['defaultvalue']['value']} ? 'selected' : null) ?>><?= $v->{$value['defaultvalue']['text']} ?></option>
                                                            <?php elseif ($value['defaultvalue']['type'] == 'array') : ?>
                                                                <option value="<?= $v[$value['defaultvalue']['value']] ?>" <?= (isset($value['defaultvalue']['selected']) && $value['defaultvalue']['selected'] == $v[$value['defaultvalue']['value']] ? 'selected' : null) ?>><?= $v[$value['defaultvalue']['text']] ?></option>
                                                            <?php endif; ?>
                                                        <?php endforeach; ?>
                                                    <?php endif; ?>
                                                </select>
                                            <?php elseif ($value['type'] == 'button') : ?>
                                                <div>
                                                    <button type="submit" class="<?= $value['class'] ?>"><?= $value['text'] ?></button>
                                                </div>
                                            <?php endif; ?>
                                        <?php else : ?>
                                            <?php if (isset($value['data'])) : ?>
                                                <div>
                                                    <?php foreach ($value['data'] as $key => $value) : ?>
                                                        <?php if ($value['type'] == 'button') : ?>
                                                            <button type="submit" class="<?= $value['class'] ?>"><?= $value['text'] ?></button>
                                                        <?php elseif ($value['type'] == 'a') : ?>
                                                            <a href="<?= $value['href'] ?>" class="<?= $value['class'] ?>"><?= $value['text'] ?></a>
                                                        <?php endif; ?>
                                                    <?php endforeach; ?>
                                                </div>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </form>
                <?php endif; ?>

                <div class="table-responsive">
                    <table class="table table-striped datatables">
                        <thead>
                            <tr>
                                <?php foreach ($element['table'] as $key => $value) : ?>
                                    <?php if (!is_array($value)) : ?>
                                        <th><?= $value ?></th>
                                    <?php else : ?>
                                        <?php foreach ($value as $k => $v) : ?>
                                            <?php if ($k == 'title') : ?>
                                                <th><?= $v ?></th>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </tr>
                        </thead>

                        <tbody>
                            <?php foreach ($element['result'] as $key => $value) : ?>
                                <tr>
                                    <?php foreach ($element['table'] as $k => $v) : ?>
                                        <?php if (!is_array($v)) : ?>
                                            <?php if (validateDate($value->$k, 'Y-m-d') || validateDate($value->$k, 'Y-m-d H:i:s')) : ?>
                                                <?php if (validateDate($value->$k, 'Y-m-d')) : ?>
                                                    <td><?= tgl_indo($value->$k) ?></td>
                                                <?php else : ?>
                                                    <td><?= tgl_indo(date('Y-m-d', strtotime($value->$k))) ?> <?= date('H:i:s', strtotime($value->$k)) ?></td>
                                                <?php endif; ?>
                                            <?php else : ?>
                                                <td><?= $value->$k ?></td>
                                            <?php endif; ?>
                                        <?php else : ?>
                                            <?php foreach ($v as $k2 => $v2) : ?>
                                                <?php if (is_array($v2)) : ?>
                                                    <td>
                                                        <?php foreach ($v2 as $k3 => $v3) : ?>
                                                            <?php $statement = ""; ?>

                                                            <?php if (isset($v3['statement'])) : ?>
                                                                <?php if (!isset($v3['statement']['type'])) : ?>
                                                                    <?php $skipped = false; ?>

                                                                    <?php foreach ($v3['statement'] as $k4 => $v4) : ?>
                                                                        <?php if ($value->{$k4} != $v4) : ?>
                                                                            <?php $skipped = true; ?>
                                                                        <?php endif; ?>
                                                                    <?php endforeach; ?>

                                                                    <?php if ($skipped) continue; ?>
                                                                <?php elseif ($v3['statement']['type'] == 'specific') : ?>
                                                                    <?php foreach ($v3['statement']['columns'] as $k4 => $v4) : ?>
                                                                        <?php if ($value->{$k4} != $v4) : ?>
                                                                            <?php foreach ($v3['statement']['actions'] as $k5 => $v5) : ?>
                                                                                <?php if ($k5 == 'hide') : ?>
                                                                                    <?php $statement .= "style='display: none;'"; ?>
                                                                                <?php endif; ?>
                                                                            <?php endforeach; ?>
                                                                        <?php endif; ?>
                                                                    <?php endforeach; ?>
                                                                <?php endif; ?>
                                                            <?php endif; ?>

                                                            <?php
                                                            $attr = isset($v3['attr']) ? $v3['attr'] : array();

                                                            $attribute = "";
                                                            foreach ($attr as $k => $v) {
                                                                $attribute .= "$k='$v' ";
                                                            }
                                                            ?>

                                                            <?php if ($v3['type'] == 'img') : ?>
                                                                <?php $attribute = ""; ?>

                                                                <?php foreach ($v3['attr'] as $k4 => $v4) : ?>
                                                                    <?php if (preg_match_all('/table.value/', $v4)) : ?>
                                                                        <?php preg_match("/(?<={).*?(?=})/", $v4, $match); ?>
                                                                        <?php $get_col = explode('.', $match[0]) ?>

                                                                        <?php if (isset($get_col[2])) : ?>
                                                                            <?php $v4 = str_replace('${table.value.' . $get_col[2] . '}', $value->{$get_col[2]} ?? 'Data', $v4); ?>
                                                                        <?php endif; ?>
                                                                    <?php endif; ?>

                                                                    <?php $attribute .= "$k4='$v4'"; ?>
                                                                <?php endforeach; ?>

                                                                <img <?= $attribute ?>>
                                                            <?php elseif ($v3['type'] == 'button') : ?>
                                                                <?php $onclick = str_replace('${table.primary}', $value->id, $v3['onclick']); ?>

                                                                <?php if (preg_match_all('/table.value/', $onclick)) : ?>
                                                                    <?php preg_match("/(?<={).*?(?=})/", $onclick, $match); ?>
                                                                    <?php $get_col = explode('.', $match[0]) ?>

                                                                    <?php if (isset($get_col[2])) : ?>
                                                                        <?php $onclick = str_replace('${table.value.' . $get_col[2] . '}', $value->{$get_col[2]} ?? 'Data', $onclick); ?>
                                                                    <?php endif; ?>
                                                                <?php endif; ?>

                                                                <button type="button" class="<?= $v3['class'] ?>" onclick="<?= $onclick ?>" <?= isset($statement) ? $statement : null ?>>
                                                                    <i class="<?= $v3['icon'] ?>"></i>

                                                                    <?php if (isset($v3['text'])) : ?>
                                                                        <span><?= $v3['text'] ?></span>
                                                                    <?php endif; ?>
                                                                </button>
                                                            <?php elseif ($v3['type'] == 'a') : ?>
                                                                <?php $href = str_replace('${table.primary}', $value->id, $v3['href']); ?>

                                                                <?php if (preg_match_all('/table.value/', $href)) : ?>
                                                                    <?php preg_match("/(?<={).*?(?=})/", $href, $match); ?>
                                                                    <?php $get_col = explode('.', $match[0]) ?>

                                                                    <?php if (isset($get_col[2])) : ?>
                                                                        <?php $href = str_replace('${table.value.' . $get_col[2] . '}', $value->{$get_col[2]} ?? 'Data', $href); ?>
                                                                    <?php endif; ?>
                                                                <?php endif; ?>

                                                                <a href="<?= $href ?>" class="<?= $v3['class'] ?>" <?= $attribute ?> <?= isset($statement) ? $statement : null ?>>
                                                                    <i class="<?= $v3['icon'] ?>"></i>

                                                                    <?php if (isset($v3['text'])) : ?>
                                                                        <span><?= $v3['text'] ?></span>
                                                                    <?php endif; ?>
                                                                </a>
                                                            <?php elseif ($v3['type'] == 'span') : ?>
                                                                <span><?= $v3['text'] ?></span>
                                                            <?php endif; ?>
                                                        <?php endforeach; ?>
                                                    </td>
                                                <?php endif; ?>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    (function() {
        document.addEventListener('click', function(e) {
            var el = e.target.closest('[data-export-url]');
            if (!el) return;
            e.preventDefault();
            var url = el.getAttribute('data-export-url') || el.getAttribute('href') || '';
            var form = document.getElementById('filters-form');
            if (form) {
                var params = new URLSearchParams(new FormData(form)).toString();
                if (params) {
                    url += (url.indexOf('?') > -1 ? '&' : '?') + params;
                }
            }
            window.open(url, '_blank');
        });
    })();
</script>
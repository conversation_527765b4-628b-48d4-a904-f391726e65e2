<?php

use Spipu\Html2Pdf\Html2Pdf;

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property TanahKasDesas $tanahkasdesas
 * @property Superadmins $admins
 */
class TanahKasDesa extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('TanahKasDesas', 'tanahkasdesas');
        $this->load->model('Superadmins', 'admins');
    }

    public function export()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $startdate = getGet('startdate');
        $enddate = getGet('enddate');

        $where = array();

        if (isVillages() || isBPD()) {
            $currentuser = getCurrentUser();
            $where['a.userid'] = $currentuser->id;
        }

        if ($startdate) {
            $where['DATE(a.tanggal_perolehan) >='] = $startdate;
        }
        if ($enddate) {
            $where['DATE(a.tanggal_perolehan) <='] = $enddate;
        }

        $tanah = $this->tanahkasdesas->select('a.*, b.name AS createdname, b.username AS createdusername')
            ->join('msusers b', 'b.id = a.createdby', 'left')
            ->order_by('a.nomor_urut', 'ASC')
            ->result($where);

        $html2pdf = new Html2Pdf('L');
        $html2pdf->writeHTML($this->load->view('master/tanahkasdesa/export', array(
            'tanah' => $tanah,
            'user' => $this->admins->get(array('id' => getCurrentIdUser()))->row(),
            'statistics' => $this->tanahkasdesas->getStatistics(getCurrentUser()->id ?? null)
        ), true));
        $html2pdf->output('Buku Tanah Kas Desa.pdf');
    }

    public function statistics()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Unauthorized');
        }

        $userid = null;
        if (isVillages() || isBPD()) {
            $userid = getCurrentUser()->id;
        }

        $statistics = $this->tanahkasdesas->getStatistics($userid);

        return JSONResponseDefault('SUCCESS', $statistics);
    }

    public function search()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Unauthorized');
        }

        $keyword = $this->input->get('q');
        if (empty($keyword)) {
            return JSONResponseDefault('SUCCESS', []);
        }

        $userid = null;
        if (isVillages() || isBPD()) {
            $userid = getCurrentUser()->id;
        }

        $results = $this->tanahkasdesas->search($keyword, $userid);

        $data = [];
        foreach ($results as $row) {
            $data[] = [
                'id' => $row->id,
                'asal_tanah_kas_desa' => $row->asal_tanah_kas_desa,
                'nomor_sertifikat' => $row->nomor_sertifikat,
                'lokasi' => $row->lokasi,
                'luas_m2' => $row->luas_m2,
                'text' => $row->asal_tanah_kas_desa . ' - ' . $row->lokasi . ' (' . $row->luas_m2 . ' m2)'
            ];
        }

        return JSONResponseDefault('SUCCESS', $data);
    }

    public function dashboard()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $userid = null;
        if (isVillages() || isBPD()) {
            $userid = getCurrentUser()->id;
        }

        $data = array();
        $data['title'] = 'Dashboard Buku Tanah Kas Desa';
        $data['content'] = 'master/tanahkasdesa/dashboard';
        $data['statistics'] = $this->tanahkasdesas->getStatistics($userid);
        $data['recent_items'] = $this->tanahkasdesas->select('*')
            ->where($userid ? ['userid' => $userid] : [])
            ->order_by('createddate', 'DESC')
            ->limit(10)
            ->result();

        return $this->load->view('master', $data);
    }

    public function chart_data()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Unauthorized');
        }

        $type = $this->input->get('type', true);
        $userid = null;
        if (isVillages() || isBPD()) {
            $userid = getCurrentUser()->id;
        }

        switch ($type) {
            case 'by_perolehan':
                $statistics = $this->tanahkasdesas->getStatistics($userid);
                $data = $statistics['by_perolehan'];
                break;
            case 'by_jenis':
                $statistics = $this->tanahkasdesas->getStatistics($userid);
                $data = $statistics['by_jenis'];
                break;
            case 'by_patok':
                $statistics = $this->tanahkasdesas->getStatistics($userid);
                $data = $statistics['by_patok'];
                break;
            case 'by_papan':
                $statistics = $this->tanahkasdesas->getStatistics($userid);
                $data = $statistics['by_papan'];
                break;
            default:
                $data = [];
        }

        return JSONResponseDefault('SUCCESS', $data);
    }

    public function import()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $data = array();
        $data['title'] = 'Import Data Tanah Kas Desa';
        $data['content'] = 'master/tanahkasdesa/import';

        return $this->load->view('master', $data);
    }

    public function process_import()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Unauthorized');
        }

        if (empty($_FILES['file']['name'])) {
            return JSONResponseDefault('FAILED', 'File Excel harus dipilih');
        }

        $config['upload_path'] = './uploads/temp/';
        $config['allowed_types'] = 'xlsx|xls';
        $config['max_size'] = 5120; // 5MB
        $config['file_name'] = 'import_tanah_' . time() . '_' . $_FILES['file']['name'];

        // Create directory if not exists
        if (!is_dir('./uploads/temp/')) {
            mkdir('./uploads/temp/', 0755, true);
        }

        $this->load->library('upload', $config);

        if (!$this->upload->do_upload('file')) {
            return JSONResponseDefault('FAILED', 'Error upload file: ' . $this->upload->display_errors());
        }

        $upload_data = $this->upload->data();
        $file_path = $upload_data['full_path'];

        try {
            // Load PhpSpreadsheet
            require_once FCPATH . 'vendor/autoload.php';

            $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader('Xlsx');
            $spreadsheet = $reader->load($file_path);
            $worksheet = $spreadsheet->getActiveSheet();
            $rows = $worksheet->toArray();

            $imported = 0;
            $errors = [];
            $userid = getCurrentUser()->id;

            // Skip header row
            for ($i = 1; $i < count($rows); $i++) {
                $row = $rows[$i];

                if (empty($row[1])) continue; // Skip if asal tanah empty

                $data = [
                    'nomor_urut' => $row[0] ?: $this->tanahkasdesas->getNextNomorUrut($userid),
                    'asal_tanah_kas_desa' => $row[1],
                    'nomor_sertifikat' => $row[2],
                    'luas_m2' => is_numeric($row[3]) ? $row[3] : null,
                    'kelas' => $row[4],
                    'perolehan_tkd' => $row[5],
                    'tanggal_perolehan' => !empty($row[6]) ? date('Y-m-d', strtotime($row[6])) : null,
                    'jenis_tkd' => $row[7],
                    'patok_tanda_batas' => $row[8] ?: 'Tidak Ada',
                    'papan_nama' => $row[9] ?: 'Tidak Ada',
                    'lokasi' => $row[10],
                    'peruntukan' => $row[11],
                    'mutasi' => $row[12],
                    'keterangan' => $row[13],
                    'userid' => $userid,
                    'createdby' => $userid,
                    'createddate' => date('Y-m-d H:i:s')
                ];

                if ($this->tanahkasdesas->insert($data)) {
                    $imported++;
                } else {
                    $errors[] = "Baris " . ($i + 1) . ": Gagal import data";
                }
            }

            // Delete uploaded file
            unlink($file_path);

            $message = "Berhasil import $imported data";
            if (!empty($errors)) {
                $message .= ". Error: " . implode(', ', array_slice($errors, 0, 5));
            }

            return JSONResponseDefault('SUCCESS', $message);
        } catch (Exception $e) {
            // Delete uploaded file if exists
            if (file_exists($file_path)) {
                unlink($file_path);
            }

            return JSONResponseDefault('FAILED', 'Error processing file: ' . $e->getMessage());
        }
    }
}

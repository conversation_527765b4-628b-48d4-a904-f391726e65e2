<?php

use Spipu\Html2Pdf\Html2Pdf;

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property BukuInventarisDesas $bukuinventarisdesas
 * @property Superadmins $admins
 */
class BukuInventarisDesa extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('BukuInventarisDesas', 'bukuinventarisdesas');
        $this->load->model('Superadmins', 'admins');
    }

    public function export()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $where = array();

        if (isVillages() || isBPD()) {
            $currentuser = getCurrentUser();
            $where['a.userid'] = $currentuser->id;
        }

        // Optional date filters from query string (same as filter form)
        $startdate = $this->input->get('startdate', true);
        $enddate = $this->input->get('enddate', true);
        if (!empty($startdate)) {
            $where['DATE(a.tanggal_penghapusan) >='] = $startdate;
        }
        if (!empty($enddate)) {
            $where['DATE(a.tanggal_penghapusan) <='] = $enddate;
        }

        $inventaris = $this->bukuinventarisdesas->select('a.*, b.name AS createdname, b.username AS createdusername')
            ->join('msusers b', 'b.id = a.createdby', 'left')
            ->order_by('a.nomor_urut', 'ASC')
            ->result($where);

        return $this->load->view('master/bukuinventarisdesa/export', array(
            'inventaris' => $inventaris,
            'user' => $this->admins->get(array('id' => getCurrentIdUser()))->row(),
            'statistics' => $this->bukuinventarisdesas->getStatistics(getCurrentUser()->id ?? null)
        ));
        exit;

        $html2pdf = new Html2Pdf('L');
        $html2pdf->writeHTML($this->load->view('master/bukuinventarisdesa/export', array(
            'inventaris' => $inventaris,
            'user' => $this->admins->get(array('id' => getCurrentIdUser()))->row(),
            'statistics' => $this->bukuinventarisdesas->getStatistics(getCurrentUser()->id ?? null)
        ), true));
        $html2pdf->output('Buku Inventaris & Kekayaan Desa.pdf');
    }

    public function statistics()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Unauthorized');
        }

        $userid = null;
        if (isVillages() || isBPD()) {
            $userid = getCurrentUser()->id;
        }

        $statistics = $this->bukuinventarisdesas->getStatistics($userid);

        return JSONResponseDefault('SUCCESS', $statistics);
    }

    public function search()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Unauthorized');
        }

        $keyword = $this->input->get('q');
        if (empty($keyword)) {
            return JSONResponseDefault('SUCCESS', []);
        }

        $userid = null;
        if (isVillages() || isBPD()) {
            $userid = getCurrentUser()->id;
        }

        $results = $this->bukuinventarisdesas->search($keyword, $userid);

        $data = [];
        foreach ($results as $row) {
            $data[] = [
                'id' => $row->id,
                'jenis_barang_bangunan' => $row->jenis_barang_bangunan,
                'asal_barang_bangunan' => $row->asal_barang_bangunan,
                'keadaan_awal_tahun' => $row->keadaan_awal_tahun,
                'keadaan_akhir_tahun' => $row->keadaan_akhir_tahun,
                'text' => $row->jenis_barang_bangunan . ' (' . $row->asal_barang_bangunan . ')'
            ];
        }

        return JSONResponseDefault('SUCCESS', $data);
    }

    public function dashboard()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $userid = null;
        if (isVillages() || isBPD()) {
            $userid = getCurrentUser()->id;
        }

        $data = array();
        $data['title'] = 'Dashboard Buku Inventaris Desa';
        $data['content'] = 'master/bukuinventarisdesa/dashboard';
        $data['statistics'] = $this->bukuinventarisdesas->getStatistics($userid);
        $data['recent_items'] = $this->bukuinventarisdesas->select('*')
            ->where($userid ? ['userid' => $userid] : [])
            ->order_by('createddate', 'DESC')
            ->limit(10)
            ->result();

        return $this->load->view('master', $data);
    }

    public function chart_data()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Unauthorized');
        }

        $type = $this->input->get('type', true);
        $userid = null;
        if (isVillages() || isBPD()) {
            $userid = getCurrentUser()->id;
        }

        switch ($type) {
            case 'by_type':
                $statistics = $this->bukuinventarisdesas->getStatistics($userid);
                $data = $statistics['by_type'];
                break;
            case 'by_source':
                $statistics = $this->bukuinventarisdesas->getStatistics($userid);
                $data = $statistics['by_source'];
                break;
            case 'by_condition_awal':
                $statistics = $this->bukuinventarisdesas->getStatistics($userid);
                $data = $statistics['by_condition_awal'];
                break;
            case 'by_condition_akhir':
                $statistics = $this->bukuinventarisdesas->getStatistics($userid);
                $data = $statistics['by_condition_akhir'];
                break;
            case 'by_penghapusan':
                $statistics = $this->bukuinventarisdesas->getStatistics($userid);
                $data = $statistics['by_penghapusan'];
                break;
            default:
                $data = [];
        }

        return JSONResponseDefault('SUCCESS', $data);
    }

    public function import()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        if (!isVillages() && !isBPD()) {
            return redirect(base_url('master/bukuinventarisdesa'));
        }

        $data = array();
        $data['title'] = 'Import Data Inventaris Desa';
        $data['content'] = 'master/bukuinventarisdesa/import';

        return $this->load->view('master', $data);
    }

    public function process_import()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Unauthorized');
        }

        if (!isVillages() && !isBPD()) {
            return JSONResponseDefault('FAILED', 'Akses ditolak');
        }

        if (empty($_FILES['excel_file']['name'])) {
            return JSONResponseDefault('FAILED', 'File Excel harus dipilih');
        }

        $config['upload_path'] = './uploads/temp/';
        $config['allowed_types'] = 'xlsx|xls|csv';
        $config['max_size'] = 10240; // 10MB
        $config['file_name'] = 'import_inventaris_' . time() . '_' . $_FILES['excel_file']['name'];

        // Create temp directory if not exists
        if (!is_dir('./uploads/temp/')) {
            mkdir('./uploads/temp/', 0755, true);
        }

        $this->load->library('upload', $config);

        if (!$this->upload->do_upload('excel_file')) {
            return JSONResponseDefault('FAILED', 'Error upload file: ' . $this->upload->display_errors());
        }

        $upload_data = $this->upload->data();
        $file_path = $upload_data['full_path'];

        try {
            $result = $this->bukuinventarisdesas->importFromExcel($file_path, getCurrentUser()->id);

            // Delete uploaded file
            unlink($file_path);

            if ($result['success'] > 0) {
                $message = "Import berhasil! {$result['success']} data berhasil diimport.";
                if ($result['errors']) {
                    $message .= " {$result['errors']} data gagal diimport.";
                }
                return JSONResponseDefault('SUCCESS', $message);
            } else {
                return JSONResponseDefault('FAILED', 'Tidak ada data yang berhasil diimport. ' . implode(', ', $result['error_messages']));
            }
        } catch (Exception $e) {
            // Delete uploaded file on error
            if (file_exists($file_path)) {
                unlink($file_path);
            }
            return JSONResponseDefault('FAILED', 'Error: ' . $e->getMessage());
        }
    }
}

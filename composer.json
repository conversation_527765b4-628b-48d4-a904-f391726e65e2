{"description": "The CodeIgniter framework", "name": "codeigniter/framework", "type": "project", "homepage": "https://codeigniter.com", "license": "MIT", "support": {"forum": "http://forum.codeigniter.com/", "wiki": "https://github.com/bcit-ci/CodeIgniter/wiki", "slack": "https://codeigniterchat.slack.com", "source": "https://github.com/bcit-ci/CodeIgniter"}, "require": {"php": ">=5.3.7", "spipu/html2pdf": "^5.2", "picqer/php-barcode-generator": "^2.2", "dompdf/dompdf": "^3.1", "phpoffice/phpword": "^1.0", "chillerlan/php-qrcode": "^5.0"}, "suggest": {"paragonie/random_compat": "Provides better randomness in PHP 5.x"}, "scripts": {"test:coverage": ["@putenv XDEBUG_MODE=coverage", "phpunit --color=always --coverage-text --configuration tests/travis/sqlite.phpunit.xml"]}, "require-dev": {"mikey179/vfsstream": "1.6.*", "phpunit/phpunit": "4.* || 5.* || 9.*"}, "config": {"platform": {"php": "8.0"}}}